<mxfile host="65bd71144e">
    <diagram id="V0ZsRTKHOcwE4ekJ1ngE" name="Page-1">
        <mxGraphModel dx="791" dy="632" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="0" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" background="#000000" math="0" shadow="0" adaptiveColors="simple">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="3" value="LLM 用户层" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="110" y="140" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="LLMEngine层" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="320" y="140" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="Executor 层" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="530" y="140" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="Worker层" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="740" y="140" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="ModelRunner层" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="950" y="140" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="LLMEngine" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="110" y="470" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="Tokenize" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="320" y="330" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="model_executer" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="320" y="420" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="init_kv_cache" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="320" y="520" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="scheduler" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="320" y="610" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=light-dark(#f0f0f0, #ededed);" edge="1" parent="1" source="9" target="11">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="430" y="480" as="sourcePoint"/>
                        <mxPoint x="480" y="430" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="280" y="500"/>
                            <mxPoint x="280" y="360"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="" style="endArrow=classic;html=1;strokeColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="9">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="310" y="480" as="sourcePoint"/>
                        <mxPoint x="320" y="450" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="280" y="500"/>
                            <mxPoint x="280" y="450"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=light-dark(#fafafa, #ededed);" edge="1" parent="1" source="9" target="13">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="310" y="480" as="sourcePoint"/>
                        <mxPoint x="360" y="430" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="280" y="500"/>
                            <mxPoint x="280" y="550"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="20" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=light-dark(#ffffff, #ededed);" edge="1" parent="1" source="9" target="14">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="310" y="480" as="sourcePoint"/>
                        <mxPoint x="360" y="430" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="280" y="500"/>
                            <mxPoint x="280" y="640"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="init_worker" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="530" y="420" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#FFFFFF;" edge="1" parent="1" source="12" target="21">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="480" y="450" as="sourcePoint"/>
                        <mxPoint x="530" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="23" value="create_worker" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="730" y="330" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="init_device" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="730" y="420" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="load_model" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="730" y="520" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="ModelRunner" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="950" y="330" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="init_device" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="950" y="420" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="load_model" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="950" y="520" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=light-dark(#fcfcfc, #ededed);" edge="1" parent="1" source="21" target="24">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="720" y="450" as="sourcePoint"/>
                        <mxPoint x="770" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="30" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=light-dark(#fcfcfc, #ededed);" edge="1" parent="1" source="24" target="27">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="860" y="449.5" as="sourcePoint"/>
                        <mxPoint x="940" y="449.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="31" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=light-dark(#fcfcfc, #ededed);" edge="1" parent="1" target="28">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="850" y="549.5" as="sourcePoint"/>
                        <mxPoint x="940" y="549" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="32" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=light-dark(#fcfcfc, #ededed);" edge="1" parent="1" target="26">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="850" y="360" as="sourcePoint"/>
                        <mxPoint x="940" y="359.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="33" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=light-dark(#fffafa, #ededed);" edge="1" parent="1" source="21" target="23">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="720" y="450" as="sourcePoint"/>
                        <mxPoint x="770" y="400" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="690" y="450"/>
                            <mxPoint x="690" y="360"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="36" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=light-dark(#f9f6f6, #ededed);" edge="1" parent="1" source="21" target="25">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="720" y="450" as="sourcePoint"/>
                        <mxPoint x="770" y="400" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="690" y="450"/>
                            <mxPoint x="690" y="550"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>